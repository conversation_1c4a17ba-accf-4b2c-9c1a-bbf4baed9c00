# 音视频还原库开发计划

## 项目目标
通过解析网络流量，还原音视频通话为WAV/MP4文件，支持协议：
- 信令协议：SIP, RTSP, H.323
- 媒体编码：G.711/G.722/G.723/G.726/G.728/G.729/Opus/Siren/H.261/H.263/H.264

1. 核心目标
通过解析网卡捕获的流量（已由外部程序提供），识别音视频信令（SIP/H.323/RTSP等）和RTP流，还原为：

音频通话 → 单一路径的WAV文件（支持G.711/G.729等编码）

视频通话 → 单一路径的MP4文件（含音轨，支持H.264/H.265）

关键要求：同一通通话的所有RTP流需合并为单一文件。

2. 功能需求
模块	功能描述
协议解析	解析SIP、H.323、RTSP信令，提取媒体信息（IP/端口/编码格式/SSRC）
RTP处理	识别RTP/RTCP包，重组乱序包、处理丢包（支持RFC 3550）
会话关联	基于Call-ID/会话ID将同一通话的多路流（音频/视频）关联
媒体还原	解码音频（G.711/G.729/GSM）→ PCM → WAV
解码视频（H.264/H.265） + 音频同步 → MP4
文件输出	生成WAV（音频通话）或MP4（视频通话），按通话会话ID命名
日志监控	实时输出处理状态（会话建立/丢包率/文件生成路径）

1. 非功能需求
类型	要求
性能	支持实时处理（延迟≤1秒），并发处理100+路通话
可靠性	网络抖动容忍（乱序重组）、部分丢包可恢复（静音填充/帧补偿）
扩展性	模块化设计，支持新增协议（如WebRTC）或编码格式
资源占用	CPU占用率≤70%（单路1080P视频），内存泄漏<1MB/小时

1. 开发流程
```
graph TD
    A[需求分析] --> B[架构设计]
    B --> C[协议栈选型]
    C --> D[模块开发]
    D --> E[集成测试]
    E --> F[性能优化]
    F --> G[部署文档]

    subgraph 关键阶段
        B --> 定义模块接口
        D --> 信令解析+会话管理
        D --> RTP引擎+媒体还原
        E --> 模拟流量测试
        F --> 抗丢包压测
    end
```
1. 推荐协议栈
层	推荐方案	理由
Live555（RTSP）	成熟开源库，支持多协议扩展
RTP引擎	JRTPLIB 或 ORT	轻量级、支持RFC 3550全功能（乱序/丢包检测）
媒体处理	FFmpeg	一站式解决编解码（G.711→PCM, H.264→YUV）
容器封装（WAV/MP4）

1. 关键挑战与解决方案
挑战	解决方案
多路流合并	通过SSRC+Call-ID关联音频/视频流，FFmpeg的avformat_alloc_output_context2混合
网络丢包	RTCP XR报告反馈 + FFmpeg丢包隐藏（音频插值/视频帧复制）
H.323信令复杂	使用H.323Plus库的H.225/H.245解析模块
实时性要求	异步处理架构：信令解析与媒体还原分离线程

1. 输出交付物
2. 

### 阶段1：协议栈开发

| 任务               | 进度 | 交付物                |
| ------------------ | ---- | --------------------- |
| H.323 ASN.1c生成器 | 0%   | `h323_parser.c/h`     |
| SIP/RTSP ragel 生成器 | 100%   | `dissector_sip.c/dissector_rtsp.c`     |
| RTP/RTCP引擎       | 0%   | `rtp_engine.cpp`      |
| 媒体会话管理器     | 0%   | `session_manager.cpp` |

### 阶段2：媒体处理核心

| 任务             | 关键点            | 风险控制                 |
| ---------------- | ----------------- | ------------------------ |
| 音频解码管道实现 | G.728自定义解码器 | 单元测试覆盖所有编码类型 |
| 视频同步机制     | 音频主导时钟策略  | 丢帧补偿算法             |
| FFmpeg封装层     | 支持元数据注入    | 内存泄漏检测             |



1. 媒体处理流水线：

```
graph LR
  A[RTP包] --> B[抖动缓冲]
  B --> C[解码线程池]
  C --> D[帧重排序队列]
  D --> E[FFmpeg编码器]
  E --> F[文件写入器]
```
